'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  ModalResponsive,
  ModalResponsiveContent,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';
import { useOrderStore } from '@/store/order/action';

const addOrderDomainSchema = z.object({
  name: z.string().min(1, 'Domain name is required'),
  nameserver_1: z.string().optional(),
  nameserver_2: z.string().optional(),
  domain_type: z.enum(['none', 'internal', 'external']).default('none'),
}).refine((data) => {
  // If domain_type is external, nameservers are required
  if (data.domain_type === 'external') {
    return data.nameserver_1 && data.nameserver_1.trim().length > 0;
  }
  return true;
}, {
  message: 'Nameserver 1 is required for external domains',
  path: ['nameserver_1']
}).refine((data) => {
  // If domain_type is external, nameserver_2 is also required
  if (data.domain_type === 'external') {
    return data.nameserver_2 && data.nameserver_2.trim().length > 0;
  }
  return true;
}, {
  message: 'Nameserver 2 is required for external domains',
  path: ['nameserver_2']
});

type AddOrderDomainForm = z.infer<typeof addOrderDomainSchema>;

interface AddOrderDomainModalProps {
  children: React.ReactNode;
  orderId: number;
  onSuccess?: () => void;
}

export function AddOrderDomainModal({
  children,
  orderId,
  onSuccess,
}: AddOrderDomainModalProps) {
  const [open, setOpen] = useState(false);
  const { createOrderDomain, creatingOrderDomain } = useOrderStore();

  const form = useForm<AddOrderDomainForm>({
    resolver: zodResolver(addOrderDomainSchema),
    defaultValues: {
      name: '',
      nameserver_1: '',
      nameserver_2: '',
      domain_type: 'none',
    },
  });

  const onSubmit = async (data: AddOrderDomainForm) => {
    try {
      const response = await createOrderDomain({
        name: data.name,
        is_available: false, // Fixed value
        order_id: orderId,
        nameserver_1: data.nameserver_1,
        nameserver_2: data.nameserver_2,
        is_internal: data.domain_type === 'internal',
        is_external: data.domain_type === 'external',
      });

      if (response?.status) {
        toast.success('Order domain added successfully');
        form.reset();
        setOpen(false);
        onSuccess?.();
      } else {
        toast.error(response?.message || 'Failed to add order domain');
      }
    } catch (error: any) {
      console.error('Failed to add order domain:', error);
      toast.error(
        error?.response?.data?.message || 'Failed to add order domain'
      );
    }
  };

  return (
    <ModalResponsive open={open} onOpenChange={setOpen}>
      <ModalResponsiveTrigger asChild>{children}</ModalResponsiveTrigger>
      <ModalResponsiveContent className='max-w-2xl max-h-[90vh] overflow-y-auto'>
        <ModalResponsiveHeader>
          <ModalResponsiveTitle>Add Order Domain</ModalResponsiveTitle>
          <ModalResponsiveDescription>
            Add a new domain to this order.
          </ModalResponsiveDescription>
        </ModalResponsiveHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Domain Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='Enter domain name (e.g., example.com)'
                      className='font-mono'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='domain_type'
              render={({ field }) => (
                <FormItem className='space-y-3'>
                  <FormLabel>Domain Type</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        // Reset nameserver fields when changing domain type
                        form.resetField('nameserver_1');
                        form.resetField('nameserver_2');
                      }}
                      value={field.value}
                      className='flex flex-row gap-6'
                    >
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='none' id='none' />
                        <FormLabel htmlFor='none'>Regular Domain</FormLabel>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='internal' id='internal' />
                        <FormLabel htmlFor='internal'>Internal</FormLabel>
                      </div>
                      <div className='flex items-center space-x-2'>
                        <RadioGroupItem value='external' id='external' />
                        <FormLabel htmlFor='external'>External</FormLabel>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              <FormField
                control={form.control}
                name='nameserver_1'
                render={({ field }) => {
                  const domainType = form.watch('domain_type');
                  const isDisabled = domainType !== 'external';
                  return (
                    <FormItem>
                      <FormLabel>Nameserver 1 {domainType === 'external' && <span className='text-red-500'>*</span>}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='ns1.example.com'
                          className='font-mono'
                          disabled={isDisabled}
                          {...field}
                          value={isDisabled ? '' : field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              <FormField
                control={form.control}
                name='nameserver_2'
                render={({ field }) => {
                  const domainType = form.watch('domain_type');
                  const isDisabled = domainType !== 'external';
                  return (
                    <FormItem>
                      <FormLabel>Nameserver 2 {domainType === 'external' && <span className='text-red-500'>*</span>}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='ns2.example.com'
                          className='font-mono'
                          disabled={isDisabled}
                          {...field}
                          value={isDisabled ? '' : field.value}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />
            </div>

            <div className='text-sm text-muted-foreground p-3 bg-muted/50 rounded-lg'>
              <p>
                New domains will be created as <strong>unavailable</strong> by
                default.
              </p>
            </div>

            <ModalResponsiveFooter className='flex flex-col-reverse gap-3 sm:flex-row sm:justify-end sm:gap-2'>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
                disabled={creatingOrderDomain}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creatingOrderDomain}>
                {creatingOrderDomain ? (
                  <>
                    <Plus className='h-4 w-4 animate-spin' />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className='h-4 w-4' />
                    Add Domain
                  </>
                )}
              </Button>
            </ModalResponsiveFooter>
          </form>
        </Form>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
