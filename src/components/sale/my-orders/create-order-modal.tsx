'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import {
  Plus,
  Trash2,
  Loader2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Search,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  ModalResponsive,
  ModalResponsiveContent,
  ModalResponsiveHeader,
  ModalResponsiveTitle,
  ModalResponsiveDescription,
  ModalResponsiveFooter,
  ModalResponsiveTrigger,
} from '@/components/ui/modal-responsive';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useAuthStore } from '@/store/auth/action';
import { useNamecheapStore } from '@/store/namecheap/action';
import { DomainCheckResult } from '@/store/namecheap/type';
import { useOrderStore } from '@/store/order/action';
import { useTemplateStore } from '@/store/template/action';

const createOrderSchema = z.object({
  line_code: z
    .string()
    .min(1, 'Line code is required')
    .refine(
      val => val === val.toLowerCase(),
      'Line code must be lowercase only'
    ),
  name: z
    .string()
    .min(1, 'Name is required')
    .refine(val => val === val.toLowerCase(), 'Name must be lowercase only'),
  description: z.string().optional(),
  template_id: z.number().min(1, 'Template is required'),
  duration: z.number().min(1, 'Duration is required'),
  order_domains: z
    .array(
      z.object({
        name: z
          .string()
          .min(1, 'Domain name is required')
          .refine(
            val => val === val.toLowerCase(),
            'Domain name must be lowercase only'
          ),
        nameserver_1: z.string().optional(),
        nameserver_2: z.string().optional(),
        is_internal: z.boolean().optional(),
        is_external: z.boolean().optional(),
      })
    )
    .min(2, 'At least 2 domains are required'),
});

type CreateOrderForm = z.infer<typeof createOrderSchema>;

interface CreateOrderModalProps {
  children: React.ReactNode;
  onSuccess?: () => void;
}

export function CreateOrderModal({
  children,
  onSuccess,
}: CreateOrderModalProps) {
  const [open, setOpen] = useState(false);
  const [domainsChecked, setDomainsChecked] = useState(false);
  const { me } = useAuthStore();
  const { createOrder, creating } = useOrderStore();
  const {
    templates,
    fetchTemplates,
    loading: templatesLoading,
  } = useTemplateStore();
  const {
    checkDomains,
    domainCheckResults,
    loading: checkingDomains,
    error: domainCheckError,
    clearResults,
    clearError,
  } = useNamecheapStore();

  const form = useForm<CreateOrderForm>({
    resolver: zodResolver(createOrderSchema),
    defaultValues: {
      line_code: '',
      name: '',
      description: '',
      template_id: 0,
      duration: 1,
      order_domains: [
        {
          name: '',
          nameserver_1: '',
          nameserver_2: '',
          is_internal: false,
          is_external: false
        },
        {
          name: '',
          nameserver_1: '',
          nameserver_2: '',
          is_internal: false,
          is_external: false
        }
      ],
    },
  });

  const {
    fields: domainFields,
    append: appendDomain,
    remove: removeDomain,
  } = useFieldArray({
    control: form.control,
    name: 'order_domains',
  });

  useEffect(() => {
    if (open && templates.length === 0) {
      fetchTemplates();
    }
  }, [open, templates.length, fetchTemplates]);

  // Reset domain check state when modal opens/closes
  useEffect(() => {
    if (!open) {
      clearResults();
      clearError();
      setDomainsChecked(false);
    }
  }, [open, clearResults, clearError]);

  const generateOrderCode = (templateName: string, userId: number): string => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;
    return `${templateName.toUpperCase()}-${userId}-${timestamp}`;
  };

  // Helper functions for domain availability
  const getDomainAvailability = (
    domainName: string
  ): DomainCheckResult | null => {
    return (
      domainCheckResults.find(result => result.Domain === domainName) || null
    );
  };

  const getUnavailableDomains = (): string[] => {
    const formDomains = form
      .getValues('order_domains')
      .map(d => d.name)
      .filter(Boolean);
    return formDomains.filter(domainName => {
      const result = getDomainAvailability(domainName);
      return result && result.Available === 'false';
    });
  };

  const handleCheckAvailability = async () => {
    const formDomains = form
      .getValues('order_domains')
      .map(d => d.name)
      .filter(Boolean);

    if (formDomains.length === 0) {
      toast.error('Please enter at least one domain name');
      return;
    }

    // Validate domain names format
    const invalidDomains = formDomains.filter(domain => !domain.includes('.'));
    if (invalidDomains.length > 0) {
      toast.error('Please enter valid domain names (e.g., example.com)');
      return;
    }

    try {
      clearError();
      const response = await checkDomains(formDomains);
      if (response?.status) {
        setDomainsChecked(true);
        toast.success('Domain availability checked successfully');
      } else {
        toast.error(response?.message || 'Failed to check domain availability');
      }
    } catch (error) {
      console.error('Failed to check domains:', error);
      toast.error('Failed to check domain availability');
    }
  };

  const onSubmit = async (data: CreateOrderForm) => {
    try {
      if (!me?.id) {
        toast.error('User information not available');
        return;
      }

      // Check if domains have been checked
      if (!domainsChecked) {
        toast.error(
          'Please check domain availability before creating the order'
        );
        return;
      }

      // Check for unavailable domains
      const unavailableDomains = getUnavailableDomains();
      if (unavailableDomains.length > 0) {
        toast.error(
          `Cannot create order: The following domains are unavailable: ${unavailableDomains.join(', ')}`
        );
        return;
      }

      const selectedTemplate = templates.find(t => t.id === data.template_id);
      if (!selectedTemplate) {
        toast.error('Selected template not found');
        return;
      }

      const generatedCode = generateOrderCode(selectedTemplate.name, me.id);

      const orderData = {
        ...data,
        code: generatedCode,
        description: data.description || '',
      };

      const response = await createOrder(orderData);
      if (response?.status) {
        toast.success('Order created successfully');
        form.reset();
        setOpen(false);
        onSuccess?.();
      } else {
        toast.error(response?.message || 'Failed to create order');
      }
    } catch (error) {
      console.error('Failed to create order:', error);
      toast.error('Failed to create order');
    }
  };

  const handleAddDomain = () => {
    appendDomain({
      name: '',
      nameserver_1: '',
      nameserver_2: '',
      is_internal: false,
      is_external: false
    });
    // Reset domain check state when domains are modified
    setDomainsChecked(false);
    clearResults();
  };

  const handleRemoveDomain = (index: number) => {
    if (domainFields.length > 2) {
      removeDomain(index);
      // Reset domain check state when domains are modified
      setDomainsChecked(false);
      clearResults();
    }
  };

  const formatPrice = (price: string, currency: string = 'USD'): string => {
    const numPrice = parseFloat(price);
    return isNaN(numPrice) ? 'N/A' : `$${numPrice.toFixed(2)} ${currency}`;
  };

  const renderDomainStatus = (domainName: string) => {
    if (!domainName || !domainsChecked) {
      return null;
    }

    const result = getDomainAvailability(domainName);
    if (!result) {
      return null;
    }

    const isAvailable = result.Available === 'true';
    const isPremium = result.IsPremiumName === 'true';

    // Determine price to display based on conditions
    let priceToShow = null;
    let currency = 'USD';

    if (isPremium && isAvailable) {
      // Premium domain that's available - use PremiumRegistrationPrice
      priceToShow = result.PremiumRegistrationPrice;
    } else if (!isPremium && isAvailable) {
      // Regular domain that's available - use Price[0].Price
      if (result.Price && result.Price.length > 0) {
        priceToShow = result.Price[0].Price;
        currency = result.Price[0].Currency;
      }
    }
    // If unavailable (regardless of premium status), don't show price

    return (
      <div className='mt-2'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            {isAvailable ? (
              <Badge
                variant='default'
                className='bg-green-100 text-green-800 border-green-200'
              >
                <CheckCircle className='h-3 w-3 mr-1' />
                Available
              </Badge>
            ) : (
              <Badge variant='destructive'>
                <XCircle className='h-3 w-3 mr-1' />
                Unavailable
              </Badge>
            )}

            {isPremium && (
              <Badge
                variant='secondary'
                className='bg-yellow-100 text-yellow-800 border-yellow-200'
              >
                <AlertCircle className='h-3 w-3 mr-1' />
                Premium
              </Badge>
            )}
          </div>

          {priceToShow && (
            <div className='text-sm font-medium text-muted-foreground'>
              {formatPrice(priceToShow, currency)}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <ModalResponsive open={open} onOpenChange={setOpen}>
      <ModalResponsiveTrigger asChild>{children}</ModalResponsiveTrigger>
      <ModalResponsiveContent className='max-w-2xl'>
        <ModalResponsiveHeader>
          <ModalResponsiveTitle>Create New Order</ModalResponsiveTitle>
          <ModalResponsiveDescription>
            Fill in the details to create a new order.
          </ModalResponsiveDescription>
        </ModalResponsiveHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
            <FormField
              control={form.control}
              name='line_code'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Line Code</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter line code' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder='Enter order name' {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='description'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder='Enter order description'
                      className='min-h-[80px]'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='template_id'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value, 10))}
                    value={field.value ? field.value.toString() : ''}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select a template' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {templatesLoading ? (
                        <SelectItem value='loading' disabled>
                          Loading templates...
                        </SelectItem>
                      ) : templates.length === 0 ? (
                        <SelectItem value='no-templates' disabled>
                          No templates available
                        </SelectItem>
                      ) : (
                        templates.map(template => (
                          <SelectItem
                            key={template.id}
                            value={template.id.toString()}
                          >
                            {template.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='duration'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duration</FormLabel>
                  <Select
                    onValueChange={value => field.onChange(parseInt(value, 10))}
                    value={field.value ? field.value.toString() : ''}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder='Select duration' />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value='1'>1 month</SelectItem>
                      <SelectItem value='3'>3 months</SelectItem>
                      <SelectItem value='6'>6 months</SelectItem>
                      <SelectItem value='12'>12 months</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className='space-y-4'>
              <div className='flex items-center justify-between'>
                <FormLabel>Order Domains</FormLabel>
                <div className='flex items-center gap-2'>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleCheckAvailability}
                    disabled={checkingDomains}
                    className='flex items-center gap-2'
                  >
                    {checkingDomains ? (
                      <>
                        <Loader2 className='h-4 w-4 animate-spin' />
                        Checking...
                      </>
                    ) : (
                      <>
                        <Search className='h-4 w-4' />
                        Check Availability
                      </>
                    )}
                  </Button>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={handleAddDomain}
                    className='flex items-center gap-2'
                  >
                    <Plus className='h-4 w-4' />
                    Add Domain
                  </Button>
                </div>
              </div>

              {domainCheckError && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>{domainCheckError}</AlertDescription>
                </Alert>
              )}

              {domainsChecked && getUnavailableDomains().length > 0 && (
                <Alert variant='destructive'>
                  <XCircle className='h-4 w-4' />
                  <AlertDescription>
                    The following domains are unavailable:{' '}
                    {getUnavailableDomains().join(', ')}. Please remove them or
                    choose different domains before creating the order.
                  </AlertDescription>
                </Alert>
              )}

              <div className='space-y-4'>
                {domainFields.map((field, index) => (
                  <div key={field.id} className='space-y-3 p-4 border rounded-lg bg-muted/30'>
                    <div className='flex items-center justify-between'>
                      <h4 className='font-medium text-sm'>Domain {index + 1}</h4>
                      {domainFields.length > 2 && (
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => handleRemoveDomain(index)}
                          className='flex items-center gap-1 text-destructive hover:text-destructive'
                        >
                          <Trash2 className='h-4 w-4' />
                        </Button>
                      )}
                    </div>

                    <div className='space-y-3'>
                      <FormField
                        control={form.control}
                        name={`order_domains.${index}.name`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Domain Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder='Enter domain name (e.g., example.com)'
                                className='font-mono'
                                {...field}
                                onChange={e => {
                                  field.onChange(e);
                                  // Reset domain check state when domain names are modified
                                  setDomainsChecked(false);
                                  clearResults();
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
                        <FormField
                          control={form.control}
                          name={`order_domains.${index}.nameserver_1`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nameserver 1</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='ns1.example.com'
                                  className='font-mono'
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`order_domains.${index}.nameserver_2`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Nameserver 2</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder='ns2.example.com'
                                  className='font-mono'
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className='flex items-center gap-6'>
                        <FormField
                          control={form.control}
                          name={`order_domains.${index}.is_internal`}
                          render={({ field }) => (
                            <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className='space-y-1 leading-none'>
                                <FormLabel>Internal</FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`order_domains.${index}.is_external`}
                          render={({ field }) => (
                            <FormItem className='flex flex-row items-start space-x-3 space-y-0'>
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className='space-y-1 leading-none'>
                                <FormLabel>External</FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {renderDomainStatus(
                      form.watch(`order_domains.${index}.name`)
                    )}
                  </div>
                ))}
              </div>
            </div>

            <ModalResponsiveFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => setOpen(false)}
                disabled={creating || checkingDomains}
              >
                Cancel
              </Button>
              <Button
                type='submit'
                disabled={
                  creating ||
                  checkingDomains ||
                  (!domainsChecked &&
                    form.getValues('order_domains').some(d => d.name))
                }
              >
                {creating ? (
                  <>
                    <Loader2 className='h-4 w-4 animate-spin mr-2' />
                    Creating...
                  </>
                ) : (
                  'Create Order'
                )}
              </Button>
            </ModalResponsiveFooter>
          </form>
        </Form>
      </ModalResponsiveContent>
    </ModalResponsive>
  );
}
